from playwright.sync_api import sync_playwright, expect
import json
import time
import logging
import re
import requests
from dataclasses import dataclass, field
from typing import List, Dict, Optional
from datetime import datetime
import ollama
import hashlib
import os

@dataclass
class TestResult:
    test_name: str
    status: str  # PASSED, FAILED, WARNING, ERROR
    details: str
    screenshot_path: str
    execution_time: float = 0.0
    severity: str = "LOW"  # LOW, MEDIUM, HIGH, CRITICAL
    recommendations: List[str] = field(default_factory=list)

@dataclass
class TestConfig:
    headless: bool = True
    viewport_width: int = 1920
    viewport_height: int = 1080
    timeout: int = 30000
    performance_thresholds: Dict = field(default_factory=lambda: {
        'load_time': 3000,
        'first_contentful_paint': 2000,
        'largest_contentful_paint': 4000
    })
    security_testing: bool = True
    edge_case_testing: bool = True
    ai_analysis: bool = True

class AITestIntelligence:
    """Enhanced AI-powered test case generation and analysis"""

    def __init__(self):
        self.ollama_available = self.check_ollama()
        self.fallback_patterns = {
            'email': [
                '<EMAIL>', 'invalid-email', '', 'test@',
                'user@domain', '<EMAIL>'
            ],
            'password': [
                'SecurePass123!', 'weak', '', '123', 'password',
                'VeryLongPasswordThatExceedsNormalLimits123456789!'
            ],
            'text': [
                'Normal text', '<script>alert("xss")</script>', '',
                'Special chars: !@#$%^&*()', 'Very long text ' * 50
            ],
            'number': ['123', '-1', '0', '999999999', 'abc', ''],
            'url': ['https://example.com', 'invalid-url', '', 'ftp://test.com']
        }
        self.malicious_payloads = [
            "<script>alert('XSS')</script>",
            "'; DROP TABLE users; --",
            "../../../etc/passwd",
            "%3Cscript%3Ealert('XSS')%3C/script%3E",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "{{7*7}}",  # Template injection
            "${7*7}",   # Expression injection
            "\x00\x01\x02\x03",  # Null bytes
        ]

    def check_ollama(self):
        """Check if Ollama is available"""
        try:
            response = requests.get('http://localhost:11434/api/tags', timeout=2)
            return response.status_code == 200
        except:
            return False

    def analyze_page_with_ai(self, page_content: str, screenshot_path: str = None):
        """Enhanced AI analysis combining both approaches"""
        if self.ollama_available:
            return self._analyze_with_ollama(page_content, screenshot_path)
        else:
            return self._analyze_with_patterns(page_content)

    def _analyze_with_ollama(self, page_content: str, screenshot_path: str = None):
        """Advanced AI analysis using Ollama with enhanced prompts"""
        try:
            prompt = f"""
            Analyze this web page content for comprehensive testing insights:

            HTML Content (first 2000 chars):
            {page_content[:2000]}

            Provide detailed analysis in JSON format:
            {{
                "usability": ["list of usability issues"],
                "security": ["security vulnerabilities and concerns"],
                "accessibility": ["accessibility problems"],
                "performance": ["performance bottlenecks"],
                "missing": ["missing functionality"],
                "tests": ["recommended test scenarios"],
                "edge_cases": ["potential edge cases to test"],
                "vulnerabilities": ["specific security vulnerabilities"]
            }}
            """

            response = requests.post('http://localhost:11434/api/generate',
                json={
                    'model': 'llama2',
                    'prompt': prompt,
                    'stream': False
                }, timeout=30)

            if response.status_code == 200:
                result = response.json()
                return self._parse_ai_response(result.get('response', ''))

        except Exception as e:
            print(f"🤖 AI analysis failed: {e}")

        return self._analyze_with_patterns(page_content)

    def _analyze_with_patterns(self, page_content: str):
        """Enhanced pattern-based analysis"""
        issues = []
        recommendations = []
        vulnerabilities = []

        # Enhanced security checks
        if 'password' in page_content.lower() and 'autocomplete' not in page_content.lower():
            issues.append("Password fields missing autocomplete attributes")
            vulnerabilities.append("Password autocomplete vulnerability")

        if '<img' in page_content and 'alt=' not in page_content:
            issues.append("Images missing alt attributes")

        if 'form' in page_content.lower() and 'csrf' not in page_content.lower():
            issues.append("Forms may be missing CSRF protection")
            vulnerabilities.append("Potential CSRF vulnerability")

        # Check for inline scripts
        if '<script>' in page_content:
            vulnerabilities.append("Inline scripts detected - potential XSS risk")

        # Check for external resources
        if 'http://' in page_content and 'https://' in page_content:
            vulnerabilities.append("Mixed content detected")

        # Generate enhanced recommendations
        if 'login' in page_content.lower():
            recommendations.extend([
                "Test login with invalid credentials",
                "Test password reset functionality",
                "Test account lockout mechanisms",
                "Test session management"
            ])

        if 'signup' in page_content.lower():
            recommendations.extend([
                "Test signup with duplicate email",
                "Test email validation",
                "Test password strength requirements",
                "Test input sanitization"
            ])

        return {
            'usability': issues[:3] if issues else ["No major usability issues detected"],
            'security': issues[3:6] if len(issues) > 3 else ["Basic security checks recommended"],
            'accessibility': issues[6:] if len(issues) > 6 else ["Accessibility review needed"],
            'performance': ["Check image optimization", "Minimize JavaScript bundles"],
            'missing': ["Consider adding loading states", "Add error boundaries"],
            'tests': recommendations if recommendations else ["Test core user flows"],
            'edge_cases': ["Test boundary conditions", "Test malformed inputs"],
            'vulnerabilities': vulnerabilities if vulnerabilities else ["No obvious vulnerabilities detected"]
        }

    def _parse_ai_response(self, response: str):
        """Parse AI response and extract JSON"""
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except:
            pass

        # Fallback to pattern analysis
        return {
            'usability': ["AI analysis completed"],
            'security': ["Security review recommended"],
            'accessibility': ["Accessibility check needed"],
            'performance': ["Performance optimization suggested"],
            'missing': ["Feature completeness review"],
            'tests': ["Comprehensive testing recommended"],
            'edge_cases': ["Edge case testing needed"],
            'vulnerabilities': ["Security audit recommended"]
        }

    def generate_smart_test_cases(self, form_html: str, form_type: str = "unknown"):
        """Generate intelligent test cases based on form analysis"""
        if self.ollama_available:
            return self._generate_with_ai(form_html, form_type)
        else:
            return self._generate_with_patterns(form_html, form_type)

    def _generate_with_ai(self, form_html: str, form_type: str):
        """AI-powered test case generation"""
        try:
            prompt = f"""
            Generate comprehensive test cases for this {form_type} form:

            {form_html}

            Generate test cases for:
            1. Valid inputs (happy path)
            2. Invalid inputs (edge cases)
            3. Security testing (XSS, injection)
            4. Boundary testing (min/max values)
            5. Accessibility testing

            Return as JSON array with: field, value, expected_result, test_type
            """

            response = requests.post('http://localhost:11434/api/generate',
                json={
                    'model': 'llama2',
                    'prompt': prompt,
                    'stream': False
                }, timeout=20)

            if response.status_code == 200:
                result = response.json()
                return self._parse_test_cases(result.get('response', ''))

        except Exception as e:
            print(f"🤖 AI test generation failed: {e}")

        return self._generate_with_patterns(form_html, form_type)

    def _generate_with_patterns(self, form_html: str, form_type: str):
        """Pattern-based test case generation"""
        test_cases = []

        # Detect field types and generate appropriate tests
        for field_type, test_values in self.fallback_patterns.items():
            if field_type in form_html.lower():
                for value in test_values:
                    test_cases.append({
                        'field': field_type,
                        'value': value,
                        'expected_result': 'valid' if value == test_values[0] else 'invalid',
                        'test_type': 'boundary' if len(value) > 50 else 'standard'
                    })

        return test_cases

    def _parse_test_cases(self, response: str):
        """Parse AI response for test cases"""
        try:
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except:
            pass

        # Fallback
        return [
            {'field': 'email', 'value': '<EMAIL>', 'expected_result': 'valid', 'test_type': 'happy_path'},
            {'field': 'email', 'value': 'invalid', 'expected_result': 'invalid', 'test_type': 'negative'}
        ]

class AIWebTester:
    def __init__(self, config: TestConfig = None):
        self.config = config or TestConfig()
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.launch(
            headless=self.config.headless,
            args=['--disable-web-security', '--disable-features=VizDisplayCompositor']
        )
        self.context = self.browser.new_context(
            viewport={'width': self.config.viewport_width, 'height': self.config.viewport_height}
        )
        self.results: List[TestResult] = []
        self.ai_intelligence = AITestIntelligence()
        self.screenshots_dir = "test-screenshots"
        os.makedirs(self.screenshots_dir, exist_ok=True)
        self.setup_logging()

        # Print AI status
        if self.ai_intelligence.ollama_available:
            self.logger.info("🧠 AI Intelligence: ENABLED (Ollama detected)")
        else:
            self.logger.info("🧠 AI Intelligence: PATTERN-BASED (Ollama not available)")
    
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('web_testing.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def run_comprehensive_test(self, url: str, custom_tests: List = None):
        """Run comprehensive web testing suite"""
        start_time = time.time()
        self.logger.info(f"Starting comprehensive test for: {url}")
        
        page = self.context.new_page()
        
        # Enable detailed metrics
        page.route('**/*', self.track_network_requests)
        
        try:
            page.goto(url, wait_until='networkidle', timeout=self.config.timeout)
        except Exception as e:
            self.results.append(TestResult(
                "page_load", "ERROR", f"Failed to load page: {str(e)}", 
                "", 0, "CRITICAL", ["Check URL accessibility and network connectivity"]
            ))
            return
        
        # Enhanced test suite with security and edge cases
        tests = custom_tests or [
            self.test_page_structure,
            self.test_accessibility_comprehensive,
            self.test_forms_advanced,
            self.test_navigation_comprehensive,
            self.test_responsive_design,
            self.test_performance_comprehensive,
            self.test_security_comprehensive,
            self.test_seo_basics,
            self.test_browser_compatibility,
            self.test_user_experience
        ]

        # Add enhanced security and edge case tests if enabled
        if self.config.security_testing:
            tests.extend([
                self.test_security_advanced,
                self.test_malicious_inputs,
                self.test_session_security
            ])

        if self.config.edge_case_testing:
            tests.extend([
                self.test_edge_case_authentication,
                self.test_edge_case_boundary_conditions,
                self.test_edge_case_concurrent_operations,
                self.test_edge_case_error_handling
            ])

        if self.config.ai_analysis:
            tests.append(self.test_ai_comprehensive_analysis)
        
        for test in tests:
            test_start = time.time()
            try:
                self.logger.info(f"Running test: {test.__name__}")
                result = test(page)
                result.execution_time = time.time() - test_start
                self.results.append(result)
            except Exception as e:
                self.results.append(TestResult(
                    test_name=test.__name__,
                    status="ERROR",
                    details=str(e),
                    screenshot_path=self.take_screenshot(page, test.__name__),
                    execution_time=time.time() - test_start,
                    severity="HIGH",
                    recommendations=[f"Fix error in {test.__name__}: {str(e)}"]
                ))
        
        page.close()
        total_time = time.time() - start_time
        self.logger.info(f"Testing completed in {total_time:.2f} seconds")
    
    def test_page_structure(self, page):
        """Test HTML structure and semantic elements"""
        issues = []
        recommendations = []
        
        # Check for semantic HTML
        has_header = page.query_selector('header') is not None
        has_nav = page.query_selector('nav') is not None
        has_main = page.query_selector('main') is not None
        has_footer = page.query_selector('footer') is not None
        
        if not has_header:
            issues.append("Missing <header> element")
            recommendations.append("Add semantic <header> element")
        if not has_main:
            issues.append("Missing <main> element")
            recommendations.append("Add semantic <main> element for primary content")
        
        # Check title and meta description
        title = page.title()
        if not title or len(title) < 10:
            issues.append("Page title missing or too short")
            recommendations.append("Add descriptive page title (50-60 characters)")
        
        meta_description = page.query_selector('meta[name="description"]')
        if not meta_description:
            issues.append("Missing meta description")
            recommendations.append("Add meta description for SEO")
        
        # Check heading hierarchy
        headings = page.query_selector_all('h1, h2, h3, h4, h5, h6')
        h1_count = len(page.query_selector_all('h1'))
        
        if h1_count == 0:
            issues.append("No H1 heading found")
            recommendations.append("Add exactly one H1 heading per page")
        elif h1_count > 1:
            issues.append(f"Multiple H1 headings found ({h1_count})")
            recommendations.append("Use only one H1 heading per page")
        
        status = "FAILED" if issues else "PASSED"
        severity = "MEDIUM" if issues else "LOW"
        
        return TestResult(
            "page_structure", status, "; ".join(issues),
            "", 0, severity, recommendations
        )
    
    def test_accessibility_comprehensive(self, page):
        """Comprehensive accessibility testing"""
        issues = []
        recommendations = []
        
        # Images without alt text
        images_without_alt = page.query_selector_all('img:not([alt])')
        if images_without_alt:
            issues.append(f"{len(images_without_alt)} images missing alt text")
            recommendations.append("Add descriptive alt attributes to all images")
        
        # Form accessibility
        inputs_without_labels = page.query_selector_all('input:not([aria-label]):not([aria-labelledby])')
        unlabeled = []
        for inp in inputs_without_labels:
            input_id = inp.get_attribute('id')
            if not input_id or not page.query_selector(f'label[for="{input_id}"]'):
                unlabeled.append(inp)
        
        if unlabeled:
            issues.append(f"{len(unlabeled)} form inputs without proper labels")
            recommendations.append("Associate labels with form inputs using 'for' attribute or aria-label")
        
        # Color contrast (basic check)
        low_contrast_elements = page.evaluate("""
            () => {
                const elements = document.querySelectorAll('*');
                let lowContrastCount = 0;
                // Basic contrast check would go here
                return lowContrastCount;
            }
        """)
        
        # Keyboard navigation
        focusable_elements = page.query_selector_all('a, button, input, select, textarea, [tabindex]')
        if len(focusable_elements) == 0:
            issues.append("No focusable elements found")
            recommendations.append("Ensure interactive elements are keyboard accessible")
        
        # ARIA landmarks
        landmarks = page.query_selector_all('[role="banner"], [role="navigation"], [role="main"], [role="contentinfo"]')
        if len(landmarks) < 2:
            issues.append("Insufficient ARIA landmarks")
            recommendations.append("Add ARIA landmarks for better screen reader navigation")
        
        status = "FAILED" if issues else "PASSED"
        severity = "HIGH" if len(issues) > 3 else "MEDIUM" if issues else "LOW"
        
        return TestResult(
            "accessibility_comprehensive", status, "; ".join(issues),
            "", 0, severity, recommendations
        )
    
    def test_forms_advanced(self, page):
        """Enhanced AI-powered form testing with security focus"""
        forms = page.query_selector_all('form')
        if not forms:
            return TestResult("forms_advanced", "PASSED", "No forms found", "", 0, "LOW", [])

        issues = []
        recommendations = []
        ai_insights = []
        tested_forms = 0

        for i, form in enumerate(forms):
            tested_forms += 1
            form_issues = []

            # Get form HTML for AI analysis
            form_html = form.inner_html()
            form_type = self._detect_form_type(form_html)

            self.logger.info(f"🤖 Analyzing {form_type} form with AI...")

            # Check form structure
            inputs = form.query_selector_all('input, textarea, select')
            submit_buttons = form.query_selector_all('button[type="submit"], input[type="submit"]')

            if not submit_buttons:
                form_issues.append("Form missing submit button")
                recommendations.append("Add submit button to form")

            # AI-powered test case generation
            test_cases = self.ai_intelligence.generate_smart_test_cases(form_html, form_type)

            # Execute AI-generated test cases
            for test_case in test_cases[:5]:  # Limit to 5 test cases per form
                matching_inputs = [inp for inp in inputs
                                 if test_case['field'].lower() in (inp.get_attribute('type') or '').lower()
                                 or test_case['field'].lower() in (inp.get_attribute('name') or '').lower()]

                for inp in matching_inputs[:1]:  # Test first matching input
                    try:
                        # Clear and fill with test value
                        inp.fill("")
                        inp.fill(test_case['value'])
                        page.wait_for_timeout(300)

                        # Check for validation messages
                        validation_msg = page.query_selector('.error, .invalid, [role="alert"]')
                        if validation_msg and test_case['expected_result'] == 'valid':
                            form_issues.append(f"Unexpected validation error for valid {test_case['field']}")

                        ai_insights.append(f"Tested {test_case['test_type']} case for {test_case['field']}")

                    except Exception as e:
                        form_issues.append(f"Error testing {test_case['field']}: {str(e)}")

            # Enhanced security testing for forms
            if self.config.security_testing:
                security_issues = self._test_form_security(form, page)
                form_issues.extend(security_issues)

            if form_issues:
                issues.extend([f"Form {i+1}: {issue}" for issue in form_issues])

        # AI-powered form analysis
        if forms and self.config.ai_analysis:
            form_analysis = self.ai_intelligence.analyze_page_with_ai(forms[0].inner_html())
            if form_analysis.get('security'):
                ai_insights.extend(form_analysis['security'][:2])
                if form_analysis.get('vulnerabilities'):
                    issues.extend(form_analysis['vulnerabilities'][:2])

        status = "FAILED" if issues else "PASSED"
        severity = "HIGH" if any("security" in issue.lower() or "vulnerability" in issue.lower() for issue in issues) else "MEDIUM" if issues else "LOW"

        details = f"AI-tested {tested_forms} forms | Insights: {len(ai_insights)} | Issues: {len(issues)}"
        if ai_insights:
            details += f" | AI: {'; '.join(ai_insights[:3])}"

        return TestResult(
            "forms_advanced", status, details,
            "", 0, severity, recommendations
        )

    def _detect_form_type(self, form_html: str):
        """Detect the type of form for better AI analysis"""
        form_lower = form_html.lower()

        if 'login' in form_lower or 'sign in' in form_lower:
            return 'login'
        elif 'signup' in form_lower or 'register' in form_lower or 'sign up' in form_lower:
            return 'signup'
        elif 'contact' in form_lower:
            return 'contact'
        elif 'search' in form_lower:
            return 'search'
        elif 'payment' in form_lower or 'checkout' in form_lower:
            return 'payment'
        else:
            return 'generic'

    def _test_form_security(self, form, page):
        """Enhanced security testing for forms"""
        security_issues = []

        # Test for CSRF protection
        csrf_token = form.query_selector('input[name*="csrf"], input[name*="token"]')
        if not csrf_token:
            security_issues.append("No CSRF token detected")

        # Test for autocomplete on sensitive fields
        password_inputs = form.query_selector_all('input[type="password"]')
        for pwd_input in password_inputs:
            autocomplete = pwd_input.get_attribute('autocomplete')
            if not autocomplete or autocomplete != 'current-password':
                security_issues.append("Password field missing proper autocomplete")

        # Test for XSS in form inputs
        inputs = form.query_selector_all('input, textarea')
        for inp in inputs[:3]:  # Test first 3 inputs
            try:
                xss_payload = "<script>alert('xss')</script>"
                inp.fill(xss_payload)
                page.wait_for_timeout(500)

                # Check if payload is reflected
                page_content = page.content()
                if xss_payload in page_content:
                    security_issues.append("Potential XSS vulnerability in form input")
                    break
            except:
                pass

        return security_issues
    
    def test_performance_comprehensive(self, page):
        """Comprehensive performance testing"""
        issues = []
        recommendations = []
        
        # Navigation timing
        timing = page.evaluate("""
            () => {
                const perf = performance.getEntriesByType('navigation')[0];
                return {
                    domContentLoaded: perf.domContentLoadedEventEnd - perf.navigationStart,
                    loadComplete: perf.loadEventEnd - perf.navigationStart,
                    firstContentfulPaint: performance.getEntriesByType('paint')
                        .find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
                };
            }
        """)
        
        # Check load times
        if timing['loadComplete'] > self.config.performance_thresholds['load_time']:
            issues.append(f"Slow page load: {timing['loadComplete']:.0f}ms")
            recommendations.append("Optimize page load time (target: <3s)")
        
        # Resource analysis
        resources = page.evaluate("""
            () => {
                const entries = performance.getEntriesByType('resource');
                return {
                    total: entries.length,
                    large_resources: entries.filter(e => e.transferSize > 100000).length,
                    slow_resources: entries.filter(e => e.duration > 1000).length
                };
            }
        """)
        
        if resources['large_resources'] > 5:
            issues.append(f"{resources['large_resources']} large resources (>100KB)")
            recommendations.append("Compress large resources and optimize images")
        
        # Image optimization
        image_analysis = page.evaluate("""
            () => {
                const images = Array.from(document.images);
                return {
                    total: images.length,
                    oversized: images.filter(img => img.naturalWidth > 2000 || img.naturalHeight > 2000).length,
                    missing_lazy: images.filter(img => !img.loading || img.loading !== 'lazy').length
                };
            }
        """)
        
        if image_analysis['oversized'] > 0:
            issues.append(f"{image_analysis['oversized']} oversized images")
            recommendations.append("Resize images appropriately for web use")
        
        status = "FAILED" if issues else "PASSED"
        severity = "HIGH" if timing['loadComplete'] > 5000 else "MEDIUM" if issues else "LOW"
        
        return TestResult(
            "performance_comprehensive", status, "; ".join(issues),
            "", 0, severity, recommendations
        )
    
    def test_security_comprehensive(self, page):
        """Comprehensive security testing"""
        issues = []
        recommendations = []
        
        # HTTPS check
        url = page.url
        if not url.startswith('https://'):
            issues.append("Page not served over HTTPS")
            recommendations.append("Implement SSL/TLS encryption")
        
        # Security headers check
        response = page.evaluate("""
            async () => {
                try {
                    const response = await fetch(window.location.href);
                    const headers = {};
                    response.headers.forEach((value, key) => {
                        headers[key] = value;
                    });
                    return headers;
                } catch (e) {
                    return {};
                }
            }
        """)
        
        security_headers = [
            'x-frame-options',
            'x-content-type-options',
            'strict-transport-security',
            'content-security-policy'
        ]
        
        missing_headers = [header for header in security_headers if header not in response]
        if missing_headers:
            issues.append(f"Missing security headers: {', '.join(missing_headers)}")
            recommendations.append("Implement security headers for better protection")
        
        # Form security
        forms = page.query_selector_all('form')
        for form in forms:
            action = form.get_attribute('action')
            method = form.get_attribute('method')
            
            if method and method.upper() == 'GET' and 'password' in form.inner_html().lower():
                issues.append("Password form using GET method")
                recommendations.append("Use POST method for sensitive form data")
        
        # External links
        external_links = page.query_selector_all('a[href^="http"]:not([rel*="noopener"])')
        if external_links:
            issues.append(f"{len(external_links)} external links without rel='noopener'")
            recommendations.append("Add rel='noopener' to external links")
        
        status = "FAILED" if issues else "PASSED"
        severity = "HIGH" if not url.startswith('https://') else "MEDIUM" if issues else "LOW"
        
        return TestResult(
            "security_comprehensive", status, "; ".join(issues),
            "", 0, severity, recommendations
        )

    def test_security_advanced(self, page):
        """Advanced security testing with penetration testing techniques"""
        issues = []
        recommendations = []
        vulnerabilities = []

        # Test for advanced XSS vectors
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>",
            "';alert('XSS');//"
        ]

        inputs = page.query_selector_all('input, textarea')
        for inp in inputs[:3]:  # Test first 3 inputs
            for payload in xss_payloads[:3]:  # Test first 3 payloads
                try:
                    inp.fill(payload)
                    page.wait_for_timeout(300)

                    # Check if payload executed or is reflected
                    page_content = page.content()
                    if payload in page_content and "<script>" in payload:
                        vulnerabilities.append("XSS vulnerability detected")
                        break
                except:
                    pass

        # Test for SQL injection
        sql_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'--",
            "' UNION SELECT * FROM users--"
        ]

        for inp in inputs[:2]:  # Test first 2 inputs
            for payload in sql_payloads[:2]:  # Test first 2 payloads
                try:
                    inp.fill(payload)
                    page.wait_for_timeout(300)

                    # Submit form if possible
                    submit_btn = page.query_selector('button[type="submit"], input[type="submit"]')
                    if submit_btn:
                        submit_btn.click()
                        page.wait_for_timeout(1000)

                        # Check for SQL error messages
                        error_content = page.content().lower()
                        if any(error in error_content for error in ['sql', 'mysql', 'postgres', 'database error']):
                            vulnerabilities.append("Potential SQL injection vulnerability")
                            break
                except:
                    pass

        # Test for path traversal
        try:
            original_url = page.url
            traversal_paths = [
                "/../../../etc/passwd",
                "/..%2f..%2f..%2fetc%2fpasswd",
                "\\..\\..\\..\\windows\\system32\\drivers\\etc\\hosts"
            ]

            for path in traversal_paths:
                try:
                    page.goto(original_url + path, wait_until="networkidle", timeout=5000)
                    content = page.content().lower()
                    if "root:" in content or "[drivers]" in content:
                        vulnerabilities.append("Path traversal vulnerability detected")
                    page.goto(original_url, wait_until="networkidle")
                except:
                    pass
        except:
            pass

        # Compile results
        if vulnerabilities:
            issues.extend(vulnerabilities)
            recommendations.extend([
                "Implement input sanitization",
                "Use parameterized queries",
                "Add CSRF protection",
                "Implement proper access controls"
            ])

        status = "FAILED" if vulnerabilities else "PASSED"
        severity = "CRITICAL" if vulnerabilities else "LOW"

        return TestResult(
            "security_advanced", status, "; ".join(issues),
            "", 0, severity, recommendations
        )

    def test_malicious_inputs(self, page):
        """Test various malicious input vectors"""
        issues = []
        recommendations = []
        edge_cases_tested = 0

        # Get all input fields
        inputs = page.query_selector_all('input, textarea')

        for input_field in inputs[:5]:  # Test first 5 inputs
            input_type = input_field.get_attribute('type') or 'text'

            # Test malicious payloads from AI intelligence
            for payload in self.ai_intelligence.malicious_payloads[:5]:
                try:
                    edge_cases_tested += 1
                    input_field.fill(payload)
                    page.wait_for_timeout(200)

                    # Check if payload is reflected
                    page_content = page.content()
                    if payload in page_content and "<script>" in payload:
                        issues.append("Malicious input reflected in page")

                    # Test extremely long inputs
                    edge_cases_tested += 1
                    very_long_input = "A" * 10000
                    input_field.fill(very_long_input)
                    page.wait_for_timeout(200)

                    # Test Unicode and special characters
                    edge_cases_tested += 1
                    unicode_input = "🚀 тест 中文 العربية ñáéíóú"
                    input_field.fill(unicode_input)
                    page.wait_for_timeout(200)

                except Exception as e:
                    issues.append(f"Error handling malicious input: {str(e)}")

        if issues:
            recommendations.extend([
                "Implement robust input validation",
                "Add length limits to inputs",
                "Sanitize user inputs",
                "Use content security policy"
            ])

        status = "FAILED" if issues else "PASSED"
        severity = "HIGH" if issues else "LOW"

        return TestResult(
            "malicious_inputs", status, f"Tested {edge_cases_tested} malicious inputs | Issues: {len(issues)}",
            "", 0, severity, recommendations
        )

    def test_session_security(self, page):
        """Test session management security"""
        issues = []
        recommendations = []
        tests_performed = 0

        try:
            # Test session fixation
            tests_performed += 1
            original_cookies = page.context.cookies()
            page.goto(page.url + "?sessionid=fixed_session_123", wait_until="networkidle")
            new_cookies = page.context.cookies()

            # Check if session ID changed (good security practice)
            session_changed = len(original_cookies) != len(new_cookies)
            if not session_changed:
                issues.append("Session ID may not be regenerated properly")

            # Test with expired cookies
            tests_performed += 1
            page.context.add_cookies([{
                'name': 'session',
                'value': 'expired_session_token',
                'domain': 'localhost',
                'path': '/',
                'expires': 0  # Expired
            }])
            page.reload()
            page.wait_for_timeout(1000)

            # Test with malformed session data
            tests_performed += 1
            page.context.add_cookies([{
                'name': 'session',
                'value': 'malformed_session_data_12345',
                'domain': 'localhost',
                'path': '/'
            }])
            page.reload()
            page.wait_for_timeout(1000)

        except Exception as e:
            issues.append(f"Session security test failed: {str(e)}")

        if issues:
            recommendations.extend([
                "Implement proper session regeneration",
                "Add session timeout mechanisms",
                "Validate session data integrity",
                "Use secure session storage"
            ])

        status = "FAILED" if issues else "PASSED"
        severity = "HIGH" if issues else "LOW"

        return TestResult(
            "session_security", status, f"Performed {tests_performed} session tests | Issues: {len(issues)}",
            "", 0, severity, recommendations
        )

    def test_edge_case_authentication(self, page):
        """Test authentication edge cases and boundary conditions"""
        issues = []
        recommendations = []
        edge_cases_tested = 0

        try:
            # Try to navigate to login page
            login_urls = ['/login', '/signin', '/auth/login']
            login_page_found = False

            for login_url in login_urls:
                try:
                    page.goto(page.url.rstrip('/') + login_url, wait_until="networkidle")
                    if page.query_selector('input[type="password"]'):
                        login_page_found = True
                        break
                except:
                    continue

            if login_page_found:
                email_input = page.query_selector('input[type="email"], input[name*="email"]')
                password_input = page.query_selector('input[type="password"]')

                if email_input and password_input:
                    # Test extremely long email
                    edge_cases_tested += 1
                    long_email = "a" * 300 + "@example.com"
                    email_input.fill(long_email)
                    password_input.fill("password123")
                    page.wait_for_timeout(500)

                    # Test SQL injection in email
                    edge_cases_tested += 1
                    email_input.fill("'; DROP TABLE users; --")
                    password_input.fill("password")
                    page.wait_for_timeout(500)

                    # Test XSS in email
                    edge_cases_tested += 1
                    email_input.fill("<script>alert('xss')</script>@test.com")
                    password_input.fill("password")
                    page.wait_for_timeout(500)

                    # Test Unicode characters
                    edge_cases_tested += 1
                    email_input.fill("тест@пример.рф")
                    password_input.fill("пароль123")
                    page.wait_for_timeout(500)

                    # Test empty password with valid email
                    edge_cases_tested += 1
                    email_input.fill("<EMAIL>")
                    password_input.fill("")
                    page.wait_for_timeout(500)

                    # Check for proper validation
                    error_msg = page.query_selector('.error, .invalid, [role="alert"]')
                    if not error_msg:
                        issues.append("Insufficient input validation on authentication")

        except Exception as e:
            issues.append(f"Authentication testing error: {str(e)}")

        if issues:
            recommendations.extend([
                "Implement robust input validation",
                "Add rate limiting for login attempts",
                "Use proper error messages",
                "Implement account lockout mechanisms"
            ])

        status = "FAILED" if issues else "PASSED"
        severity = "HIGH" if issues else "MEDIUM"

        return TestResult(
            "edge_case_authentication", status, f"Tested {edge_cases_tested} auth edge cases | Issues: {len(issues)}",
            "", 0, severity, recommendations
        )

    def test_edge_case_boundary_conditions(self, page):
        """Test boundary conditions and limits"""
        issues = []
        recommendations = []
        edge_cases_tested = 0

        try:
            # Test with extremely large viewport
            edge_cases_tested += 1
            original_viewport = page.viewport_size
            page.set_viewport_size({"width": 8000, "height": 6000})
            page.wait_for_timeout(1000)

            # Check for layout issues
            has_horizontal_scroll = page.evaluate("document.body.scrollWidth > window.innerWidth")
            if has_horizontal_scroll:
                issues.append("Layout breaks at very large viewport")

            # Test with extremely small viewport
            edge_cases_tested += 1
            page.set_viewport_size({"width": 200, "height": 200})
            page.wait_for_timeout(1000)

            # Check if content is still accessible
            visible_elements = page.query_selector_all(':visible')
            if len(visible_elements) < 5:
                issues.append("Too few elements visible at very small viewport")

            # Reset viewport
            page.set_viewport_size(original_viewport)

            # Test rapid navigation
            edge_cases_tested += 1
            current_url = page.url
            for i in range(5):
                page.goto(current_url + "#test" + str(i))
                page.wait_for_timeout(100)

            # Test input boundary conditions
            inputs = page.query_selector_all('input, textarea')
            for input_field in inputs[:3]:  # Test first 3 inputs
                edge_cases_tested += 1
                max_length = input_field.get_attribute('maxlength')
                if max_length:
                    max_val = int(max_length)
                    # Test exactly at boundary
                    input_field.fill("a" * max_val)
                    page.wait_for_timeout(200)
                    # Test one character over boundary
                    input_field.fill("a" * (max_val + 1))
                    page.wait_for_timeout(200)

        except Exception as e:
            issues.append(f"Boundary testing error: {str(e)}")

        if issues:
            recommendations.extend([
                "Implement responsive design breakpoints",
                "Add proper input length validation",
                "Test layout at extreme viewport sizes",
                "Implement graceful degradation"
            ])

        status = "FAILED" if issues else "PASSED"
        severity = "MEDIUM" if issues else "LOW"

        return TestResult(
            "edge_case_boundary_conditions", status, f"Tested {edge_cases_tested} boundary conditions | Issues: {len(issues)}",
            "", 0, severity, recommendations
        )

    def test_edge_case_concurrent_operations(self, page):
        """Test concurrent operations and race conditions"""
        issues = []
        recommendations = []
        edge_cases_tested = 0

        try:
            # Test multiple rapid form submissions
            edge_cases_tested += 1
            forms = page.query_selector_all('form')
            if forms:
                form = forms[0]
                submit_btn = form.query_selector('button[type="submit"], input[type="submit"]')
                if submit_btn:
                    # Rapid multiple clicks
                    for i in range(5):
                        submit_btn.click()
                        page.wait_for_timeout(50)

            # Test rapid navigation while loading
            edge_cases_tested += 1
            current_url = page.url
            page.goto(current_url + "?test=1")
            page.goto(current_url + "?test=2")
            page.goto(current_url + "?test=3")
            page.wait_for_timeout(1000)

            # Test multiple AJAX requests simulation
            edge_cases_tested += 1
            page.evaluate("""
                for(let i = 0; i < 10; i++) {
                    fetch('/api/test', {method: 'GET'}).catch(() => {});
                }
            """)
            page.wait_for_timeout(2000)

        except Exception as e:
            issues.append(f"Concurrent operations test failed: {str(e)}")

        if issues:
            recommendations.extend([
                "Implement request debouncing",
                "Add proper loading states",
                "Handle concurrent requests gracefully",
                "Implement rate limiting"
            ])

        status = "FAILED" if issues else "PASSED"
        severity = "MEDIUM" if issues else "LOW"

        return TestResult(
            "edge_case_concurrent_operations", status, f"Tested {edge_cases_tested} concurrent operations | Issues: {len(issues)}",
            "", 0, severity, recommendations
        )

    def test_edge_case_error_handling(self, page):
        """Test error handling edge cases"""
        issues = []
        recommendations = []
        edge_cases_tested = 0

        try:
            # Test navigation to non-existent page
            edge_cases_tested += 1
            original_url = page.url
            page.goto(original_url + "/non-existent-page-12345", wait_until="networkidle")

            # Check if proper 404 page is shown
            page_content = page.content().lower()
            if "404" not in page_content and "not found" not in page_content:
                issues.append("No proper 404 error page")

            # Return to original page
            page.goto(original_url, wait_until="networkidle")

            # Test with malformed URLs
            edge_cases_tested += 1
            malformed_urls = [
                original_url + "/../../../etc/passwd",
                original_url + "/%2e%2e%2f%2e%2e%2f",
                original_url + "/..%2f..%2f",
            ]

            for malformed_url in malformed_urls:
                try:
                    page.goto(malformed_url, wait_until="networkidle", timeout=5000)
                    # Should not expose sensitive information
                    content = page.content().lower()
                    if "root:" in content or "password" in content:
                        issues.append("Potential path traversal vulnerability")
                    page.goto(original_url, wait_until="networkidle")
                except:
                    pass  # Expected to fail

            # Test JavaScript errors
            edge_cases_tested += 1
            try:
                page.evaluate("throw new Error('Test error')")
                page.wait_for_timeout(1000)
            except:
                pass  # Expected

        except Exception as e:
            issues.append(f"Error handling test failed: {str(e)}")

        if issues:
            recommendations.extend([
                "Implement proper 404 error pages",
                "Add path traversal protection",
                "Implement error boundaries",
                "Add proper error logging"
            ])

        status = "FAILED" if issues else "PASSED"
        severity = "HIGH" if any("vulnerability" in issue for issue in issues) else "MEDIUM" if issues else "LOW"

        return TestResult(
            "edge_case_error_handling", status, f"Tested {edge_cases_tested} error handling cases | Issues: {len(issues)}",
            "", 0, severity, recommendations
        )

    def test_ai_comprehensive_analysis(self, page):
        """Enhanced AI-powered comprehensive page analysis"""
        self.logger.info("🧠 Running AI comprehensive analysis...")

        # Get page content for AI analysis
        page_content = page.content()
        screenshot_path = self.take_screenshot(page, "ai_analysis")

        # Run AI analysis
        ai_insights = self.ai_intelligence.analyze_page_with_ai(page_content, screenshot_path)

        # Compile findings
        findings = []
        critical_issues = 0
        vulnerabilities = []

        for category, issues in ai_insights.items():
            if issues and len(issues) > 0:
                findings.extend([f"{category.title()}: {issue}" for issue in issues[:2]])
                if category in ['security', 'vulnerabilities']:
                    critical_issues += len(issues)
                    if category == 'vulnerabilities':
                        vulnerabilities.extend(issues)

        # Generate recommendations
        recommendations = ai_insights.get('tests', [])
        if ai_insights.get('edge_cases'):
            recommendations.extend(ai_insights['edge_cases'][:2])

        # Determine severity based on findings
        if vulnerabilities:
            severity = "CRITICAL"
            status = "FAILED"
        elif critical_issues > 2:
            severity = "HIGH"
            status = "FAILED"
        elif critical_issues > 0:
            severity = "MEDIUM"
            status = "WARNING"
        else:
            severity = "LOW"
            status = "PASSED"

        details = f"AI analyzed {len(ai_insights)} categories | Critical: {critical_issues} | Vulnerabilities: {len(vulnerabilities)} | Recommendations: {len(recommendations)}"

        if findings:
            details += f" | Key findings: {'; '.join(findings[:3])}"

        if vulnerabilities:
            details += f" | Security issues: {'; '.join(vulnerabilities[:2])}"

        return TestResult(
            "ai_comprehensive_analysis", status, details, screenshot_path, 0, severity, recommendations
        )
    
    def test_responsive_design(self, page):
        """Test responsive design across different viewports"""
        issues = []
        recommendations = []
        
        viewports = [
            {'width': 320, 'height': 568, 'name': 'Mobile'},
            {'width': 768, 'height': 1024, 'name': 'Tablet'},
            {'width': 1920, 'height': 1080, 'name': 'Desktop'}
        ]
        
        original_viewport = page.viewport_size
        
        for viewport in viewports:
            page.set_viewport_size({'width': viewport['width'], 'height': viewport['height']})
            page.wait_for_timeout(500)  # Allow layout to settle
            
            # Check for horizontal scrollbar
            has_horizontal_scroll = page.evaluate("document.body.scrollWidth > window.innerWidth")
            if has_horizontal_scroll:
                issues.append(f"Horizontal scroll on {viewport['name']}")
                recommendations.append(f"Fix horizontal overflow on {viewport['name']} devices")
            
            # Check for mobile-specific issues
            if viewport['name'] == 'Mobile':
                # Check viewport meta tag
                viewport_meta = page.query_selector('meta[name="viewport"]')
                if not viewport_meta:
                    issues.append("Missing viewport meta tag")
                    recommendations.append("Add viewport meta tag for mobile optimization")
                
                # Check touch targets
                buttons = page.query_selector_all('button, a, input[type="button"], input[type="submit"]')
                for button in buttons[:5]:  # Check first 5 buttons
                    box = button.bounding_box()
                    if box and (box['width'] < 44 or box['height'] < 44):
                        issues.append("Touch targets too small (< 44px)")
                        recommendations.append("Increase touch target size to at least 44x44px")
                        break
        
        # Reset viewport
        page.set_viewport_size(original_viewport)
        
        status = "FAILED" if issues else "PASSED"
        severity = "MEDIUM" if issues else "LOW"
        
        return TestResult(
            "responsive_design", status, "; ".join(issues),
            "", 0, severity, recommendations
        )
    
    def test_seo_basics(self, page):
        """Basic SEO testing"""
        issues = []
        recommendations = []
        
        # Title optimization
        title = page.title()
        if not title:
            issues.append("Missing page title")
            recommendations.append("Add descriptive page title")
        elif len(title) > 60:
            issues.append("Title too long")
            recommendations.append("Keep title under 60 characters")
        
        # Meta description
        meta_desc = page.query_selector('meta[name="description"]')
        if meta_desc:
            content = meta_desc.get_attribute('content')
            if len(content) > 160:
                issues.append("Meta description too long")
                recommendations.append("Keep meta description under 160 characters")
        
        # Structured data
        structured_data = page.query_selector_all('script[type="application/ld+json"]')
        if not structured_data:
            recommendations.append("Consider adding structured data for better SEO")
        
        # Internal linking
        internal_links = page.query_selector_all('a[href^="/"], a[href^="./"]')
        if len(internal_links) < 3:
            issues.append("Limited internal linking")
            recommendations.append("Add more internal links for better SEO")
        
        status = "FAILED" if issues else "PASSED"
        
        return TestResult(
            "seo_basics", status, "; ".join(issues),
            "", 0, "LOW", recommendations
        )
    
    def test_user_experience(self, page):
        """User experience testing"""
        issues = []
        recommendations = []
        
        # Loading indicators
        page.reload(wait_until='domcontentloaded')
        has_loading_indicator = page.query_selector('.loading, .spinner, [aria-label*="loading"]')
        
        # 404 error handling
        broken_links = []
        links = page.query_selector_all('a[href]')[:10]  # Test first 10 links
        for link in links:
            href = link.get_attribute('href')
            if href and href.startswith('http'):
                try:
                    response = page.goto(href, wait_until='domcontentloaded', timeout=5000)
                    if response and response.status >= 400:
                        broken_links.append(href)
                except:
                    broken_links.append(href)
        
        if broken_links:
            issues.append(f"{len(broken_links)} broken links found")
            recommendations.append("Fix or remove broken links")
        
        status = "FAILED" if issues else "PASSED"
        
        return TestResult(
            "user_experience", status, "; ".join(issues),
            "", 0, "MEDIUM" if issues else "LOW", recommendations
        )
    
    def test_navigation_comprehensive(self, page):
        """Comprehensive navigation testing"""
        issues = []
        recommendations = []
        
        # Check for navigation element
        nav_elements = page.query_selector_all('nav, [role="navigation"]')
        if not nav_elements:
            issues.append("No navigation element found")
            recommendations.append("Add semantic navigation element")
        
        # Check navigation links
        nav_links = page.query_selector_all('nav a, [role="navigation"] a')
        if len(nav_links) < 3:
            issues.append("Limited navigation options")
            recommendations.append("Provide adequate navigation options")
        
        # Breadcrumb check
        breadcrumbs = page.query_selector('[aria-label*="breadcrumb"], .breadcrumb, nav[aria-label*="breadcrumb"]')
        if not breadcrumbs and len(nav_links) > 5:
            recommendations.append("Consider adding breadcrumb navigation for deep site structures")
        
        # Test keyboard navigation
        try:
            first_link = nav_links[0] if nav_links else page.query_selector('a')
            if first_link:
                first_link.focus()
                page.keyboard.press('Tab')
                focused_element = page.evaluate('document.activeElement.tagName')
                if not focused_element:
                    issues.append("Keyboard navigation not working properly")
                    recommendations.append("Ensure all interactive elements are keyboard accessible")
        except:
            pass
        
        # Check for skip links
        skip_link = page.query_selector('a[href="#main"], a[href="#content"], .skip-link')
        if not skip_link:
            recommendations.append("Add skip navigation link for accessibility")
        
        status = "FAILED" if issues else "PASSED"
        
        return TestResult(
            "navigation_comprehensive", status, "; ".join(issues),
            "", 0, "MEDIUM" if issues else "LOW", recommendations
        )
    
    def test_browser_compatibility(self, page):
        """Test browser-specific features and compatibility"""
        issues = []
        recommendations = []
        
        # Check for modern JS features without fallbacks
        js_errors = page.evaluate("""
            () => {
                const errors = [];
                
                // Check for console errors
                window.addEventListener('error', (e) => {
                    errors.push(e.message);
                });
                
                // Test common compatibility issues
                try {
                    // ES6 features
                    eval('const test = () => {};');
                    eval('const {x} = {x: 1};');
                } catch (e) {
                    errors.push('ES6 syntax not supported');
                }
                
                return errors;
            }
        """)
        
        if js_errors:
            issues.append(f"JavaScript compatibility issues: {'; '.join(js_errors[:3])}")
            recommendations.append("Add polyfills or transpile modern JavaScript")
        
        # Check CSS compatibility
        css_issues = page.evaluate("""
            () => {
                const issues = [];
                const testElement = document.createElement('div');
                document.body.appendChild(testElement);

                // Test modern CSS features
                const cssTests = [
                    ['grid', 'display: grid'],
                    ['flexbox', 'display: flex'],
                    ['css-variables', '--test: red'],
                    ['transforms', 'transform: rotate(45deg)']
                ];

                cssTests.forEach(([feature, css]) => {
                    testElement.style.cssText = css;
                    const computed = getComputedStyle(testElement);
                    if (!computed.getPropertyValue(css.split(':')[0].trim())) {
                        issues.push(`${feature} not supported`);
                    }
                });

                document.body.removeChild(testElement);
                return issues;
            }
        """)
        
        if css_issues:
            issues.extend(css_issues[:3])  # Limit to first 3 issues
            recommendations.append("Add CSS fallbacks for better browser support")
        
        status = "FAILED" if issues else "PASSED"
        
        return TestResult(
            "browser_compatibility", status, "; ".join(issues),
            "", 0, "LOW", recommendations
        )
    
    def generate_and_execute_ai_form_tests(self, page, form):
        """Generate and execute AI-powered form tests"""
        try:
            form_html = form.inner_html()
            form_structure = self.analyze_form_structure(form)
            
            prompt = f"""
            Analyze this form and generate comprehensive test cases:
            
            Form HTML: {form_html[:1000]}...
            
            Form Structure: {json.dumps(form_structure, indent=2)}
            
            Generate test cases in JSON format for:
            1. Valid input scenarios
            2. Invalid input scenarios (boundary testing)
            3. Security testing (XSS, SQL injection attempts)
            4. Accessibility testing
            5. Usability testing
            
            Response format:
            {{
                "test_cases": [
                    {{
                        "name": "test_name",
                        "selector": "css_selector",
                        "input_value": "test_value",
                        "expected_result": "expected_outcome",
                        "test_type": "validation|security|accessibility"
                    }}
                ]
            }}
            """
            
            # Use Ollama for AI analysis
            response = ollama.chat(model='llama2', messages=[
                {'role': 'user', 'content': prompt}
            ])
            
            ai_tests = json.loads(response['message']['content'])
            return self.execute_ai_test_cases(page, form, ai_tests['test_cases'])
            
        except Exception as e:
            self.logger.warning(f"AI form test generation failed: {e}")
            return self.execute_default_form_tests(page, form)
    
    def analyze_form_structure(self, form):
        """Analyze form structure for AI context"""
        inputs = form.query_selector_all('input, textarea, select')
        structure = []
        
        for inp in inputs:
            structure.append({
                'tag': inp.tag_name,
                'type': inp.get_attribute('type'),
                'name': inp.get_attribute('name'),
                'id': inp.get_attribute('id'),
                'required': inp.get_attribute('required') is not None,
                'placeholder': inp.get_attribute('placeholder')
            })
        
        return structure
    
    def execute_ai_test_cases(self, page, form, test_cases):
        """Execute AI-generated test cases"""
        issues = []
        recommendations = []
        
        for test_case in test_cases[:10]:  # Limit to 10 test cases
            try:
                selector = test_case.get('selector')
                input_value = test_case.get('input_value')
                test_type = test_case.get('test_type', 'validation')
                
                element = form.query_selector(selector)
                if not element:
                    continue
                
                # Execute test
                element.clear()
                element.fill(input_value)
                
                # Check for immediate validation feedback
                page.wait_for_timeout(500)
                validation_msg = page.query_selector('.error, .invalid, [aria-invalid="true"]')
                
                if test_type == 'security' and not validation_msg:
                    issues.append(f"Security test failed: {test_case.get('name')}")
                    recommendations.append("Implement proper input sanitization")
                
            except Exception as e:
                self.logger.debug(f"Test case execution failed: {e}")
        
        return {'issues': issues, 'recommendations': recommendations}
    
    def execute_default_form_tests(self, page, form):
        """Fallback form testing when AI is unavailable"""
        issues = []
        recommendations = []
        
        # Test common input types
        email_inputs = form.query_selector_all('input[type="email"]')
        for email_input in email_inputs:
            try:
                email_input.fill('invalid-email')
                page.wait_for_timeout(500)
                if not page.query_selector('.error, [aria-invalid="true"]'):
                    issues.append("Email validation missing")
                    recommendations.append("Add email format validation")
            except:
                pass
        
        # Test required fields
        required_inputs = form.query_selector_all('input[required], textarea[required]')
        required_inputs = form.query_selector_all('input[required], textarea[required]')
        for req_input in required_inputs:
            try:
                req_input.clear()
                # Try to submit form with empty required field
                submit_btn = form.query_selector('button[type="submit"], input[type="submit"]')
                if submit_btn:
                    submit_btn.click()
                    page.wait_for_timeout(500)
                    if not page.query_selector('.error, [aria-invalid="true"]'):
                        issues.append("Required field validation missing")
                        recommendations.append("Add validation for required fields")
            except:
                pass
        
        return {'issues': issues, 'recommendations': recommendations}
    
    def track_network_requests(self, route):
        """Track network requests for performance analysis"""
        response = route.continue_()
        # Log slow requests, failed requests, etc.
        return response
    
    def take_screenshot(self, page, test_name=""):
        """Take screenshot with timestamp and test name"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"screenshot_{test_name}_{timestamp}.png"
        path = os.path.join("screenshots", filename)
        
        # Create screenshots directory if it doesn't exist
        os.makedirs("screenshots", exist_ok=True)
        
        try:
            page.screenshot(path=path, full_page=True)
            return path
        except:
            return ""
    
    def generate_comprehensive_report(self):
        """Generate enhanced HTML report with security insights and AI analysis"""
        passed = len([r for r in self.results if r.status == "PASSED"])
        failed = len([r for r in self.results if r.status == "FAILED"])
        warnings = len([r for r in self.results if r.status == "WARNING"])
        errors = len([r for r in self.results if r.status == "ERROR"])

        total_execution_time = sum(r.execution_time for r in self.results)

        # Calculate severity distribution
        severity_counts = {}
        security_issues = 0
        ai_insights_count = 0

        for result in self.results:
            severity_counts[result.severity] = severity_counts.get(result.severity, 0) + 1

            # Count security-related issues
            if any(keyword in result.test_name.lower() for keyword in ['security', 'malicious', 'session', 'auth']):
                if result.status in ["FAILED", "WARNING"]:
                    security_issues += 1

            # Count AI insights
            if 'ai_' in result.test_name.lower():
                ai_insights_count += 1

        # Check if AI was used
        ai_status = "🧠 ENABLED" if self.ai_intelligence.ollama_available else "🔧 PATTERN-BASED"
        
        # Generate enhanced HTML report
        html_report = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>🤖 Super Testerator - Enhanced Web Testing Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; }}
                .ai-status {{ background: #28a745; color: white; padding: 10px; border-radius: 5px; margin: 10px 0; text-align: center; }}
                .summary {{ display: flex; gap: 20px; margin: 20px 0; flex-wrap: wrap; }}
                .metric {{ background: #fff; border: 1px solid #ddd; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .passed {{ border-left: 6px solid #28a745; }}
                .failed {{ border-left: 6px solid #dc3545; }}
                .warning {{ border-left: 6px solid #ffc107; }}
                .error {{ border-left: 6px solid #6f42c1; }}
                .security {{ border-left: 6px solid #fd7e14; }}
                .ai-insights {{ border-left: 6px solid #20c997; }}
                .test-result {{ margin: 15px 0; padding: 20px; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .recommendations {{ background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px; }}
                .critical {{ background: #ffebee; border-left: 6px solid #f44336; }}
                .high {{ background: #fff3e0; border-left: 6px solid #ff9800; }}
                .medium {{ background: #f3e5f5; border-left: 6px solid #9c27b0; }}
                .low {{ background: #e8f5e8; border-left: 6px solid #4caf50; }}
                .security-section {{ background: #fff5f5; border: 2px solid #ff6b6b; border-radius: 10px; padding: 20px; margin: 20px 0; }}
                .ai-section {{ background: #f0fff4; border: 2px solid #00d4aa; border-radius: 10px; padding: 20px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🤖 Super Testerator Report</h1>
                <h2>Enhanced Web Testing with AI & Security Analysis</h2>
                <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>Total Execution Time: {total_execution_time:.2f} seconds</p>
            </div>

            <div class="ai-status">
                <h3>🧠 AI Intelligence: {ai_status}</h3>
                <p>Enhanced with security testing, edge case analysis, and AI-powered insights</p>
            </div>

            <div class="summary">
                <div class="metric passed">
                    <h3>{passed}</h3>
                    <p>✅ Passed</p>
                </div>
                <div class="metric failed">
                    <h3>{failed}</h3>
                    <p>❌ Failed</p>
                </div>
                <div class="metric warning">
                    <h3>{warnings}</h3>
                    <p>⚠️ Warnings</p>
                </div>
                <div class="metric error">
                    <h3>{errors}</h3>
                    <p>🚨 Errors</p>
                </div>
                <div class="metric security">
                    <h3>{security_issues}</h3>
                    <p>🔒 Security Issues</p>
                </div>
                <div class="metric ai-insights">
                    <h3>{ai_insights_count}</h3>
                    <p>🧠 AI Insights</p>
                </div>
            </div>

            <div class="security-section">
                <h2>🔒 Security Analysis Summary</h2>
                <p><strong>Security Issues Found:</strong> {security_issues}</p>
                <p><strong>Security Tests Performed:</strong> Advanced XSS, SQL Injection, Path Traversal, Session Management</p>
                <p><strong>Edge Cases Tested:</strong> Authentication boundaries, malicious inputs, concurrent operations</p>
            </div>

            <div class="ai-section">
                <h2>🧠 AI Analysis Summary</h2>
                <p><strong>AI Status:</strong> {ai_status}</p>
                <p><strong>AI Insights Generated:</strong> {ai_insights_count}</p>
                <p><strong>Analysis Categories:</strong> Usability, Security, Accessibility, Performance, Vulnerabilities</p>
            </div>

            <h2>📊 Severity Distribution</h2>
            <ul>
        """
        
        for severity, count in severity_counts.items():
            html_report += f"<li>{severity}: {count} issues</li>"
        
        html_report += """
            </ul>
            
            <h2>Detailed Results</h2>
        """
        
        for result in self.results:
            status_class = result.status.lower()
            severity_class = result.severity.lower()
            
            html_report += f"""
            <div class="test-result {status_class} {severity_class}">
                <h3>{result.test_name.replace('_', ' ').title()}</h3>
                <p><strong>Status:</strong> {result.status}</p>
                <p><strong>Severity:</strong> {result.severity}</p>
                <p><strong>Execution Time:</strong> {result.execution_time:.2f}s</p>
                {f'<p><strong>Details:</strong> {result.details}</p>' if result.details else ''}
                
                {f'<div class="recommendations"><h4>Recommendations:</h4><ul>' + ''.join([f'<li>{rec}</li>' for rec in result.recommendations]) + '</ul></div>' if result.recommendations else ''}
                
                {f'<p><strong>Screenshot:</strong> <a href="{result.screenshot_path}" target="_blank">View Screenshot</a></p>' if result.screenshot_path else ''}
            </div>
            """
        
        html_report += """
        </body>
        </html>
        """
        
        # Save HTML report
        with open(f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html", "w") as f:
            f.write(html_report)
        
        return html_report
    
    def generate_json_report(self):
        """Generate machine-readable JSON report"""
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'passed': len([r for r in self.results if r.status == "PASSED"]),
                'failed': len([r for r in self.results if r.status == "FAILED"]),
                'warnings': len([r for r in self.results if r.status == "WARNING"]),
                'errors': len([r for r in self.results if r.status == "ERROR"]),
                'total_execution_time': sum(r.execution_time for r in self.results)
            },
            'results': [
                {
                    'test_name': r.test_name,
                    'status': r.status,
                    'severity': r.severity,
                    'details': r.details,
                    'execution_time': r.execution_time,
                    'recommendations': r.recommendations,
                    'screenshot_path': r.screenshot_path
                }
                for r in self.results
            ]
        }
        
        filename = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        return report_data
    
    def cleanup(self):
        """Clean up browser resources"""
        if hasattr(self, 'browser'):
            self.browser.close()
        if hasattr(self, 'playwright'):
            self.playwright.stop()

# Enhanced Super Testerator with AI and Security Testing
if __name__ == "__main__":
    import sys

    # Enhanced configuration with security and AI features
    config = TestConfig(
        headless=True,
        viewport_width=1920,
        viewport_height=1080,
        performance_thresholds={
            'load_time': 2500,     # More strict
            'first_contentful_paint': 1500,
            'largest_contentful_paint': 3000
        },
        security_testing=True,      # Enable advanced security testing
        edge_case_testing=True,     # Enable edge case testing
        ai_analysis=True            # Enable AI analysis
    )

    # Command line usage
    if len(sys.argv) > 1:
        url = sys.argv[1]
        task = sys.argv[2] if len(sys.argv) > 2 else "comprehensive security testing"

        print(f"🚀 Starting Super Testerator for: {url}")
        print(f"📋 Task: {task}")
        print(f"🔒 Security Testing: {'ENABLED' if config.security_testing else 'DISABLED'}")
        print(f"🧪 Edge Case Testing: {'ENABLED' if config.edge_case_testing else 'DISABLED'}")
        print(f"🧠 AI Analysis: {'ENABLED' if config.ai_analysis else 'DISABLED'}")

        tester = AIWebTester(config)

        try:
            tester.run_comprehensive_test(url)

            # Generate enhanced reports
            html_report = tester.generate_comprehensive_report()
            json_report = tester.generate_json_report()

            print(f"\n🎉 Super Testerator completed!")
            print(f"📄 HTML report created with enhanced security insights")
            print(f"📊 JSON report created for automation")
            print(f"🧪 Total tests run: {len(tester.results)}")

            # Enhanced summary with security metrics
            summary = json_report['summary']
            security_issues = len([r for r in tester.results if any(keyword in r.test_name.lower() for keyword in ['security', 'malicious', 'session']) and r.status in ["FAILED", "WARNING"]])

            print(f"\n📊 Enhanced Summary:")
            print(f"✅ Passed: {summary['passed']}")
            print(f"❌ Failed: {summary['failed']}")
            print(f"⚠️  Warnings: {summary['warnings']}")
            print(f"🚫 Errors: {summary['errors']}")
            print(f"🔒 Security Issues: {security_issues}")
            print(f"🧠 AI Status: {'ENABLED' if tester.ai_intelligence.ollama_available else 'PATTERN-BASED'}")

        finally:
            tester.cleanup()

    else:
        # Demo mode - test multiple URLs
        print("🤖 Super Testerator - Demo Mode")
        print("Usage: python testerrrrrat <url> [task_description]")
        print("\nExample:")
        print("python testerrrrrat https://example.com 'security audit'")
        print("python testerrrrrat http://localhost:3000 'comprehensive testing'")

        # Demo with example URLs
        tester = AIWebTester(config)

        try:
            demo_urls = [
                "https://example.com",
                "https://httpbin.org/forms/post"
            ]

            for url in demo_urls:
                print(f"\n=== Demo Testing {url} ===")
                try:
                    tester.run_comprehensive_test(url)
                except Exception as e:
                    print(f"Demo test failed for {url}: {e}")

            if tester.results:
                # Generate reports
                html_report = tester.generate_comprehensive_report()
                json_report = tester.generate_json_report()

                print(f"\n🎉 Demo completed! Check the generated reports.")

        finally:
            tester.cleanup()
